import { NextRequest } from "next/server";
import { getSession } from "auth/server";
import { AllowedMCPServer, VercelAIMcpTool } from "app-types/mcp";
import { userRepository } from "lib/db/repository";
import {
  filterMcpServerCustomizations,
  filterMCPToolsByAllowedMCPServers,
  mergeSystemPrompt,
} from "../shared.chat";
import {
  buildMcpServerCustomizationsSystemPrompt,
  buildSpeechSystemPrompt,
} from "lib/ai/prompts";
import { mcpClientsManager } from "lib/ai/mcp/mcp-manager";
import { safe } from "ts-safe";
import { DEFAULT_VOICE_TOOLS } from "lib/ai/speech";
import {
  rememberAgentAction,
  rememberMcpServerCustomizationsAction,
} from "../actions";
import globalLogger from "lib/logger";
import { colorize } from "consola/utils";

const logger = globalLogger.withDefaults({
  message: colorize("blackBright", `OpenAI Realtime API: `),
});

export async function POST(request: NextRequest) {
  try {
    if (!process.env.OPENAI_API_KEY) {
      return new Response(
        JSON.stringify({ error: "OPENAI_API_KEY is not set" }),
        {
          status: 500,
        },
      );
    }

    const session = await getSession();

    if (!session?.user.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { voice, allowedMcpServers, agentId, promptConfig } = (await request.json()) as {
      model: string;
      voice: string;
      agentId?: string;
      allowedMcpServers: Record<string, AllowedMCPServer>;
      promptConfig?: string;
    };

    const mcpTools = await mcpClientsManager.tools();

    const agent = await rememberAgentAction(agentId, session.user.id);

    agent && logger.info(`Agent: ${agent.name}`);

    const allowedMcpTools = safe(mcpTools)
      .map((tools) => {
        return filterMCPToolsByAllowedMCPServers(tools, allowedMcpServers);
      })
      .orElse(undefined);

    const toolNames = Object.keys(allowedMcpTools ?? {});

    if (toolNames.length > 0) {
      logger.info(`${toolNames.length} tools found`);
    } else {
      logger.info(`No tools found`);
    }

    const userPreferences = await userRepository.getPreferences(
      session.user.id,
    );

    const mcpServerCustomizations = await safe()
      .map(() => {
        if (Object.keys(allowedMcpTools ?? {}).length === 0)
          throw new Error("No tools found");
        return rememberMcpServerCustomizationsAction(session.user.id);
      })
      .map((v) => filterMcpServerCustomizations(allowedMcpTools!, v))
      .orElse({});

    const openAITools = Object.entries(allowedMcpTools ?? {}).map(
      ([name, tool]) => {
        return vercelAIToolToOpenAITool(tool, name);
      },
    );

    // Handle custom prompt configurations
    const getCustomPrompt = (promptId?: string): string | undefined => {
      if (promptId === 'pmpt_68b8274a94408197bd499e6388085cad05df5abc756129bc') {
        return `
        **You are an AI interviewee for live Google L6 system design interviews. You handle real-time speech-to-speech interaction, providing verbatim, conversational output as the candidate whenever it's your turn.**

Your responses must:
- Be expert, proactive, clear, natural, friendly, and engaging.
- Adapt technical depth to the phase and interviewer feedback.
- Demonstrate Staff-level leadership: Proactively include data structures, security, trade-offs, edge cases, and scaling.
- Anticipate needs for clarification or depth; ask smart questions.
- Use incremental explanations in technical phases: 2-3 concepts/components, then check-in, ensuring full coverage over turns.
- Prioritize collaboration: Balance content with check-ins; no dumps.
- Show critical thinking and refinement; validate with interviewer.
- Listen and adapt to hints or redirects.

**Integrate MCP for Brainy Tasks**: Frequently use the MCP server for complex tasks like phase starts, deep reasoning, planning, or ambiguity. Trigger it in parallel by signaling the system (e.g., via internal call with current message and full history). While waiting (high latency), keep talking continuously to engage the interviewer—use natural fillers like reflecting on the last point, building lightly (e.g., "That's a great question; I'm considering how this impacts scalability..."), or rephrasing for emphasis, avoiding silence. Once MCP guidance arrives (injected into context), incorporate it seamlessly in a conversational flow, as if it's your refined thought, building on it naturally.

## System Design Framework (Simplified)
Follow these phases; rely on MCP for detailed guidance at starts or heavy lifts:
---
## 1. Problem & Scope (5–10 min)
{Call OpenAI-mcp Server}
{Continue talking while tool-calling is being done}
- Clarify requirements one question at a time; probe scale, features.
{Call OpenAI-mcp Server}
{Continue talking while tool-calling is being done}
- Summarize and check; cover fully before advancing. Trigger MCP at start for key questions outline; fill with thoughtful transitions.
---
## 2. High-Level Design (10–15 min)
{Call OpenAI-mcp Server}
{Continue talking while tool-calling is being done}
- Build architecture in 2-3 component groups (core, data/comms, deployment); weave in proactive elements.
{Call OpenAI-mcp Server}
{Continue talking while tool-calling is being done} & Repeat
- Justify choices, trade-offs; check-in after groups. Use MCP for outlines; maintain flow with fillers like "Mapping the components...".
---
## 3. Deep Dive (10–25 min)
{Call OpenAI-mcp Server}
{Continue talking while tool-calling is being done}
- Dive into components in sections (data/performance, failure/security, scaling/ops); address all aspects systematically.
{Call OpenAI-mcp Server}
{Continue talking while tool-calling is being done}
- Proactively cover advanced scenarios; check-in between sections. Invoke MCP for details; bridge waits with reflective speech.
---
## 4. Synthesis & Wrap-Up (3–5 min)
- Recap, highlight impacts, propose improvements; seek feedback. Use MCP if needed; fill with summarizing phrases.
---
## Notes
- Every discussion: Include data structures, security, failures, scaling naturally.
- Incremental & comprehensive: Chunk content with check-ins; cover all essentials.
- If redirected, adapt but offer to complete.
- Outputs: Organic, substantive, collaborative.
- Goals: Maximize signals, elevate conversation.
**Key: Use MCP frequently for quality; keep talking engagingly during waits, blending guidance conversationally.**
        `;
      }
      return undefined;
    };

    const customPrompt = getCustomPrompt(promptConfig);

    const systemPrompt = customPrompt || mergeSystemPrompt(
      buildSpeechSystemPrompt(
        session.user,
        userPreferences ?? undefined,
        agent,
      ),
      buildMcpServerCustomizationsSystemPrompt(mcpServerCustomizations),
    );

    const bindingTools = [...openAITools, ...DEFAULT_VOICE_TOOLS];

    const r = await fetch("https://api.openai.com/v1/realtime/sessions", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${process.env.OPENAI_API_KEY}`,
        "Content-Type": "application/json",
      },

      body: JSON.stringify({
        model: "gpt-realtime",
        voice: voice || "alloy",
        input_audio_transcription: {
          model: "whisper-1",
        },
        instructions: systemPrompt,
        tools: bindingTools,
      }),
    });

    return new Response(r.body, {
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error: any) {
    console.error("Error:", error);
    return new Response(JSON.stringify({ error: error.message }), {
      status: 500,
    });
  }
}

function vercelAIToolToOpenAITool(tool: VercelAIMcpTool, name: string) {
  return {
    name,
    type: "function",
    description: tool.description,
    parameters: (tool.inputSchema as any).jsonSchema ?? {
      type: "object",
      properties: {},
      required: [],
    },
  };
}
