# Voice Chat Prompt Configuration

This document describes how to configure custom prompts for the OpenAI Realtime Voice Chat feature.

## Overview

The voice chat implementation now supports custom prompt configurations that override the default system prompt. This allows for specialized voice assistants with specific behaviors and expertise.

## Implementation Details

### Backend Changes

1. **API Route Enhancement** (`src/app/api/chat/openai-realtime/route.ts`):
   - Added `promptConfig` parameter to the request body
   - Implemented `getCustomPrompt()` function to handle predefined prompt configurations
   - Modified system prompt logic to use custom prompts when specified

2. **Prompt Configuration System**:
   - Prompt configurations are identified by unique IDs
   - Currently supports the System Design Interview prompt (`pmpt_68b8274a94408197bd499e6388085cad05df5abc756129bc`)
   - Falls back to default system prompt when no custom prompt is specified

### Frontend Changes

1. **App Store** (`src/app/store/index.ts`):
   - Added `promptConfig` field to voice chat options
   - Maintains selected prompt configuration across sessions

2. **Voice Chat Hook** (`src/lib/ai/speech/open-ai/use-voice-chat.openai.ts`):
   - Passes `promptConfig` parameter to the API
   - Includes prompt configuration in session creation

3. **UI Component** (`src/components/chat-bot-voice.tsx`):
   - Added "Prompt Config" dropdown in voice chat settings
   - Allows users to select between "Default" and "System Design Interview" prompts
   - Visual indicators show currently selected prompt

## Usage

### For Users

1. Open voice chat by clicking the voice chat button or using the keyboard shortcut
2. Click the settings gear icon in the voice chat interface
3. Navigate to "Prompt Config" in the dropdown menu
4. Select your desired prompt configuration:
   - **Default**: Uses the standard voice assistant prompt
   - **System Design Interview**: Specialized prompt for system design interview practice

### For Developers

To add a new prompt configuration:

1. **Add the prompt to the backend**:
   ```typescript
   const getCustomPrompt = (promptId?: string): string | undefined => {
     if (promptId === 'your_new_prompt_id') {
       return `Your custom prompt content here...`;
     }
     // ... existing prompts
     return undefined;
   };
   ```

2. **Add UI option in the frontend**:
   ```tsx
   <DropdownMenuItem
     className="cursor-pointer flex items-center justify-between"
     onClick={() =>
       appStoreMutate({
         voiceChat: {
           ...voiceChat,
           options: {
             ...voiceChat.options,
             promptConfig: 'your_new_prompt_id',
           },
         },
       })
     }
   >
     Your Prompt Name
     {voiceChat.options.promptConfig === 'your_new_prompt_id' && (
       <CheckIcon className="size-3.5" />
     )}
   </DropdownMenuItem>
   ```

## System Design Interview Prompt

The included System Design Interview prompt (`pmpt_68b8274a94408197bd499e6388085cad05df5abc756129bc`) is designed to:

- Act as an AI interviewee for L6 Staff-level system design interviews
- Provide expert, conversational responses as if speaking for the candidate
- Follow a structured 4-phase interview framework:
  1. Problem Understanding & Design Scope (5-10 minutes)
  2. High-Level Design & Buy-In (10-15 minutes)
  3. Technical Deep Dive (10-25 minutes)
  4. Strategic Synthesis & Wrap-Up (3-5 minutes)

- Demonstrate staff-level technical leadership
- Balance technical depth with collaborative dialogue
- Proactively surface data structures, security, tradeoffs, and scaling considerations

## Technical Notes

- Custom prompts completely override the default system prompt
- MCP tool customizations are still applied when using custom prompts
- The prompt configuration is stored in the app state and persists during the session
- Changes to prompt configuration require restarting the voice chat session to take effect

## Future Enhancements

Potential improvements for this feature:

1. **Dynamic Prompt Loading**: Load prompts from a database or external configuration
2. **User-Defined Prompts**: Allow users to create and save their own custom prompts
3. **Prompt Templates**: Provide template variables for dynamic prompt generation
4. **Prompt Sharing**: Enable sharing of custom prompts between users
5. **Prompt Versioning**: Support multiple versions of the same prompt configuration
